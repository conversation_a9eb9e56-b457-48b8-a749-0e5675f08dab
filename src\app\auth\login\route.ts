import { NextRequest, NextResponse } from "next/server";
import { KeycloakConfiguration } from "@/config/keycloack/config/keycloak.config";

interface ILoginErrorResponse {
	error: string;
	message: string;
}

interface ILoginRequestParams {
	redirectPath: string;
}



export async function GET(request: NextRequest): Promise<NextResponse> {
	try {
		const { redirectPath } = extractLoginParams(request);
		console.log("[auth/login] Parâmetros de redirecionamento:", redirectPath);

		if (!isValidRedirectPath(redirectPath)) {
			return createErrorResponse("invalid_redirect", "Path de redirecionamento inválido", 400);
		}

		// Gera URL do Keycloak diretamente
		const keycloakConfig = KeycloakConfiguration.getInstance();
		const state = encodeURIComponent(redirectPath); // Usa o redirectPath como state
		const keycloakUrl = keycloakConfig.buildAuthorizationUrl(state);

		console.log("[auth/login] Redirecionando para Keycloak:", keycloakUrl);
		return NextResponse.redirect(keycloakUrl, { status: 302 });
	} catch (error) {
		console.error("Erro no redirecionamento para Keycloak:", error);
		return createErrorResponse("keycloak_error", "Erro interno ao processar redirecionamento para autenticação", 500);
	}
}

function extractLoginParams(request: NextRequest): ILoginRequestParams {
	const { searchParams } = new URL(request.url);
	return {
		redirectPath: searchParams.get("redirect") || "/",
	};
}

function createErrorResponse(error: string, message: string, status: number): NextResponse {
	return NextResponse.json({ error, message } as ILoginErrorResponse, { status });
}



function isValidRedirectPath(path: string): boolean {
	const hasProtocol = path.includes("://") || path.startsWith("//");
	const hasValidStart = path.startsWith("/");
	const hasDangerousChars = ["<", ">", '"', "'", "&"].some(char => path.includes(char));
	if (hasProtocol || !hasValidStart || hasDangerousChars) return false;
	const allowedPaths = ["/", "/variaveis", "/forbidden"];
	return allowedPaths.some(allowedPath => path === allowedPath || path.startsWith(allowedPath + "/"));
}
