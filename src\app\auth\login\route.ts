import { NextRequest, NextResponse } from "next/server";

interface ILoginErrorResponse {
	error: string;
	message: string;
}

interface ILoginRequestParams {
	redirectPath: string;
}

interface IBackendResponse {
	status: number;
	location?: string;
	errorText?: string;
}

export async function GET(request: NextRequest): Promise<NextResponse> {
	try {
		const { redirectPath } = extractLoginParams(request);
		console.log("[auth/login] Parâmetros de redirecionamento:", redirectPath);
		if (!isValidRedirectPath(redirectPath)) return createErrorResponse("invalid_redirect", "Path de redirecionamento inválido", 400);
		const backendResponse = await fetchBackendLoginUrl(redirectPath);

		if (isRedirectResponse(backendResponse)) return handleRedirectResponse(backendResponse);
		return handleBackendError(backendResponse);
	} catch (error) {
		console.error("Erro no redirecionamento para Keycloak:", error);
		return createErrorResponse("keycloak_error", "Erro interno ao processar redirecionamento para autenticação", 500);
	} 
}

function extractLoginParams(request: NextRequest): ILoginRequestParams {
	const { searchParams } = new URL(request.url);
	return {
		redirectPath: searchParams.get("redirect") || "/",
	};
}

function createErrorResponse(error: string, message: string, status: number): NextResponse {
	return NextResponse.json({ error, message } as ILoginErrorResponse, { status });
}

async function fetchBackendLoginUrl(redirectPath: string): Promise<IBackendResponse> {
	const backendLoginUrl = new URL("/auth/login", process.env.API_ROUTE);
	if (redirectPath !== "/") backendLoginUrl.searchParams.set("redirect", redirectPath);
	const response = await fetch(backendLoginUrl.toString(), {
		method: "GET",
		redirect: "manual",
	});

	return {
		status: response.status,
		location: response.headers.get("location") || undefined,
		errorText: response.status !== 302 ? await response.text() : undefined,
	};
}

function isRedirectResponse(response: IBackendResponse): boolean {
	return response.status === 302;
}

function handleRedirectResponse(response: IBackendResponse): NextResponse {
	const { location } = response;
	console.log("Redirecionando para Keycloak:", location);

	if (!location) throw new Error("URL de redirecionamento não encontrada na resposta do backend");
	

	return NextResponse.redirect(location, { status: 302 });
}

function handleBackendError(response: IBackendResponse): NextResponse {
	console.error("Erro na API do backend:", {
		status: response.status,
		response: response.errorText,
	});

	return createErrorResponse("backend_error", "Erro na comunicação com o servidor de autenticação", 500);
}

function isValidRedirectPath(path: string): boolean {
	const hasProtocol = path.includes("://") || path.startsWith("//");
	const hasValidStart = path.startsWith("/");
	const hasDangerousChars = ["<", ">", '"', "'", "&"].some(char => path.includes(char));
	if (hasProtocol || !hasValidStart || hasDangerousChars) return false;
	const allowedPaths = ["/", "/variaveis", "/forbidden"];
	return allowedPaths.some(allowedPath => path === allowedPath || path.startsWith(allowedPath + "/"));
}
