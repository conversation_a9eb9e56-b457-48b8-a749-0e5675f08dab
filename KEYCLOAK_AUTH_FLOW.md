# Fluxo de Autenticação Keycloak - Documentação

## Resumo da Implementação

A implementação completa do fluxo de autenticação Keycloak foi criada para capturar o token de acesso após o login e salvá-lo usando o sistema de cookies existente (`src\shared\lib\cookies\crud\create.ts`).

## Arquivos Criados/Modificados

### 1. `/src/app/auth/login/route.ts`
- **Função**: Inicia o processo de autenticação
- **Modificações**: 
  - Removido o redirecionamento para o backend
  - Implementado redirecionamento direto para o Keycloak
  - Adicionado armazenamento do `state` em cookie para proteção CSRF

### 2. `/src/app/auth/callback/route.ts` (NOVO)
- **Função**: Processa o retorno do Keycloak após autenticação
- **Implementa**:
  - Validação do `state` para prevenir CSRF
  - Troca do código de autorização por token de acesso
  - Salvamento do token usando `createCookie`
  - Redirecionamento para a página de destino

### 3. `/src/config/keycloack/config/keycloak.config.ts`
- **Modificação**: Descomentado o `redirect_uri` no `buildAuthorizationUrl`

## Fluxo de Autenticação

### 1. Início do Login (`/auth/login`)
```
1. Usuário acessa /auth/login?redirect=/caminho/desejado
2. Sistema gera state único para CSRF protection
3. State é armazenado em cookie seguro (10 min TTL)
4. Usuário é redirecionado para Keycloak
```

### 2. Autenticação no Keycloak
```
1. Usuário faz login no Keycloak
2. Keycloak redireciona para /auth/callback com code e state
```

### 3. Processamento do Callback (`/auth/callback`)
```
1. Valida parâmetros recebidos (code, state)
2. Recupera state armazenado do cookie
3. Valida state para prevenir CSRF
4. Troca code por access_token via Keycloak
5. Salva access_token usando createCookie com:
   - Nome: "access-token" (COOKIE_NAMES.ACCESS_TOKEN)
   - Segurança: httpOnly, secure, sameSite=strict
   - TTL: baseado na expiração do JWT
6. Limpa state usado
7. Redireciona para página de destino
```

## Sistema de Cookies

O token é salvo usando o sistema existente de cookies:

- **Arquivo**: `src\shared\lib\cookies\crud\create.ts`
- **Função**: `createCookie()`
- **Configuração**:
  - Cookie assinado para segurança
  - HttpOnly para prevenir XSS
  - Secure para HTTPS
  - SameSite=strict para CSRF protection
  - TTL baseado na expiração do JWT

## Variáveis de Ambiente Necessárias

```env
KEYCLOAK_BASE_URL=https://seu-keycloak.com/
KEYCLOAK_REALM=seu-realm
KEYCLOAK_CLIENT_ID=seu-client-id
NEXT_PUBLIC_APP_URL=https://seu-app.com
```

## Configuração do Cliente Keycloak

- **Valid Redirect URIs**: `https://seu-app.com/auth/callback`
- **Web Origins**: `https://seu-app.com`
- **Access Type**: public ou confidential

## Segurança Implementada

1. **CSRF Protection**: State parameter validation
2. **Secure Cookies**: HttpOnly, Secure, SameSite
3. **JWT Validation**: Automatic expiration based on token TTL
4. **State Cleanup**: Used state is immediately removed
5. **Input Validation**: Path validation and parameter checking

## Integração Existente

A implementação reutiliza:
- Sistema de cookies existente (`createCookie`, `getCookie`, `removeCookie`)
- Função `setAuthTokens` para compatibilidade
- Middleware de autenticação existente
- Constantes de nomes de cookies (`COOKIE_NAMES`)

## Como Testar

1. Acesse `/auth/login?redirect=/variaveis`
2. Faça login no Keycloak
3. Verifique se foi redirecionado para `/variaveis`
4. Verifique se o cookie `access-token` foi criado
5. Acesse uma página protegida para validar a autenticação
