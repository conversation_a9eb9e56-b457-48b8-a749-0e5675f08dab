import { AccessTokenValidator } from "@/config/services/access-token-validator";
import { NextRequest, NextResponse } from "next/server";

export async function authMiddleware(request: Readonly<NextRequest>): Promise<NextResponse> {
	const { pathname } = request.nextUrl;
	const accessTokenValidator = new AccessTokenValidator();
	const validationResult = await accessTokenValidator.validate(request);
	if (!validationResult.isValid || validationResult.status !== "ok") {
		const keycloakLoginUrl = new URL("/auth/login", request.url);
		keycloakLoginUrl.searchParams.set("redirect", pathname);
		return NextResponse.redirect(keycloakLoginUrl, { status: 302 });
	}

	return NextResponse.next();
}
