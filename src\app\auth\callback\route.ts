import { NextRequest, NextResponse } from "next/server";
import { setAuthTokens } from "@/core/auth/lib/auth-actions";
import { KeycloakAuthenticationService } from "@/config/keycloack/services/keycloak-auth.service";

interface ICallbackErrorResponse {
	error: string;
	message: string;
}

interface ICallbackParams {
	code: string;
	state: string;
	sessionState?: string;
}

export async function GET(request: NextRequest): Promise<NextResponse> {
	try {
		const callbackParams = extractCallbackParams(request);
		console.log("[auth/callback] Processando callback do Keycloak:", callbackParams);

		if (!isValidCallbackParams(callbackParams)) {
			return createErrorResponse("invalid_params", "Parâmetros de callback inválidos", 400);
		}

		// Processa diretamente com o Keycloak
		const keycloakAuth = new KeycloakAuthenticationService();

		// Troca o código por token
		const tokenResponse = await keycloakAuth.exchangeCodeForToken(callbackParams.code, callbackParams.state);

		if (!tokenResponse.success) {
			console.error("[auth/callback] Erro ao obter token:", tokenResponse.data.message);
			return createErrorResponse("token_error", tokenResponse.data.message, 500);
		}

		// Salva o token usando createCookie
		const saveResult = await setAuthTokens(
			tokenResponse.data.access_token,
			tokenResponse.data.refresh_token
		);

		if (!saveResult.success) {
			console.error("[auth/callback] Erro ao salvar tokens:", saveResult.data.message);
			return createErrorResponse("save_error", "Erro ao salvar tokens de autenticação", 500);
		}

		// Extrai URL de redirecionamento do state
		const redirectUrl = extractRedirectFromState(callbackParams.state) || "/";
		console.log("[auth/callback] Redirecionando para:", redirectUrl);

		return NextResponse.redirect(new URL(redirectUrl, request.url));
	} catch (error) {
		console.error("Erro no processamento do callback:", error);
		return createErrorResponse("callback_error", "Erro interno ao processar callback de autenticação", 500);
	}
}

function extractCallbackParams(request: NextRequest): ICallbackParams {
	const { searchParams } = new URL(request.url);
	return {
		code: searchParams.get("code") || "",
		state: searchParams.get("state") || "",
		sessionState: searchParams.get("session_state") || undefined,
	};
}

function isValidCallbackParams(params: ICallbackParams): boolean {
	return !!(params.code && params.state);
}

function createErrorResponse(error: string, message: string, status: number): NextResponse {
	return NextResponse.json({ error, message } as ICallbackErrorResponse, { status });
}



function extractRedirectFromState(state: string): string | null {
	try {
		// O state pode conter a URL de redirecionamento codificada
		const decodedState = decodeURIComponent(state);
		
		// Se o state é uma URL completa, extrai o pathname
		if (decodedState.startsWith("http")) {
			const url = new URL(decodedState);
			return url.pathname + url.search;
		}
		
		// Se o state é um path relativo válido
		if (decodedState.startsWith("/")) {
			return decodedState;
		}
		
		return null;
	} catch (error) {
		console.warn("[auth/callback] Erro ao extrair redirect do state:", error);
		return null;
	}
}


