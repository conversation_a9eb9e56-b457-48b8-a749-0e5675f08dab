import { NextRequest, NextResponse } from "next/server";
import { setAuthTokens } from "@/core/auth/lib/auth-actions";

interface ICallbackErrorResponse {
	error: string;
	message: string;
}

interface ICallbackParams {
	code: string;
	state: string;
	sessionState?: string;
}

interface IBackendTokenResponse {
	status: number;
	cookies?: string[];
	accessToken?: string;
	refreshToken?: string;
	redirectUrl?: string;
	errorText?: string;
}

export async function GET(request: NextRequest): Promise<NextResponse> {
	try {
		const callbackParams = extractCallbackParams(request);
		console.log("[auth/callback] Processando callback do Keycloak:", callbackParams);

		if (!isValidCallbackParams(callbackParams)) {
			return createErrorResponse("invalid_params", "Parâmetros de callback inválidos", 400);
		}

		const backendResponse = await fetchBackendCallback(callbackParams);

		if (isSuccessResponse(backendResponse)) {
			return await handleSuccessResponse(backendResponse, callbackParams.state);
		}

		return handleBackendError(backendResponse);
	} catch (error) {
		console.error("Erro no processamento do callback:", error);
		return createErrorResponse("callback_error", "Erro interno ao processar callback de autenticação", 500);
	}
}

function extractCallbackParams(request: NextRequest): ICallbackParams {
	const { searchParams } = new URL(request.url);
	return {
		code: searchParams.get("code") || "",
		state: searchParams.get("state") || "",
		sessionState: searchParams.get("session_state") || undefined,
	};
}

function isValidCallbackParams(params: ICallbackParams): boolean {
	return !!(params.code && params.state);
}

function createErrorResponse(error: string, message: string, status: number): NextResponse {
	return NextResponse.json({ error, message } as ICallbackErrorResponse, { status });
}

async function fetchBackendCallback(params: ICallbackParams): Promise<IBackendTokenResponse> {
	const backendCallbackUrl = new URL("/auth/callback", process.env.API_ROUTE);
	backendCallbackUrl.searchParams.set("code", params.code);
	backendCallbackUrl.searchParams.set("state", params.state);
	if (params.sessionState) {
		backendCallbackUrl.searchParams.set("session_state", params.sessionState);
	}

	console.log("[auth/callback] Chamando backend:", backendCallbackUrl.toString());

	const response = await fetch(backendCallbackUrl.toString(), {
		method: "GET",
		redirect: "manual",
		credentials: "include", // Importante para receber cookies do backend
	});

	const responseData: IBackendTokenResponse = {
		status: response.status,
		cookies: response.headers.getSetCookie?.() || [],
		errorText: response.status !== 200 ? await response.text() : undefined,
	};

	console.log("[auth/callback] Resposta do backend:", {
		status: responseData.status,
		cookiesCount: responseData.cookies?.length || 0,
		cookies: responseData.cookies,
	});

	// Se o backend retornou sucesso, tenta extrair dados do corpo da resposta
	if (response.status === 200) {
		try {
			const body = await response.json();
			responseData.accessToken = body.access_token || body.accessToken;
			responseData.refreshToken = body.refresh_token || body.refreshToken;
			responseData.redirectUrl = body.redirect_url || body.redirectUrl;
		} catch (error) {
			console.warn("[auth/callback] Erro ao parsear resposta JSON do backend:", error);
		}
	}

	return responseData;
}

function isSuccessResponse(response: IBackendTokenResponse): boolean {
	return response.status === 200;
}

async function handleSuccessResponse(response: IBackendTokenResponse, state: string): Promise<NextResponse> {
	try {
		// Extrai a URL de redirecionamento do state ou usa padrão
		const redirectUrl = extractRedirectFromState(state) || "/";
		console.log("[auth/callback] URL de redirecionamento:", redirectUrl);

		// Se o backend forneceu tokens diretamente, salva eles
		if (response.accessToken) {
			console.log("[auth/callback] Salvando tokens fornecidos pelo backend");
			const tokenResult = await setAuthTokens(response.accessToken, response.refreshToken);
			
			if (!tokenResult.success) {
				console.error("[auth/callback] Erro ao salvar tokens:", tokenResult.data.message);
				return createErrorResponse("token_save_error", "Erro ao salvar tokens de autenticação", 500);
			}
		}

		// Cria a resposta de redirecionamento
		const redirectResponse = NextResponse.redirect(new URL(redirectUrl, process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"));

		// Se o backend enviou cookies via Set-Cookie headers, propaga eles
		if (response.cookies && response.cookies.length > 0) {
			console.log("[auth/callback] Propagando cookies do backend:", response.cookies.length);
			response.cookies.forEach(cookie => {
				redirectResponse.headers.append("Set-Cookie", cookie);
			});
		}

		return redirectResponse;
	} catch (error) {
		console.error("[auth/callback] Erro ao processar resposta de sucesso:", error);
		return createErrorResponse("success_processing_error", "Erro ao processar autenticação bem-sucedida", 500);
	}
}

function extractRedirectFromState(state: string): string | null {
	try {
		// O state pode conter a URL de redirecionamento codificada
		const decodedState = decodeURIComponent(state);
		
		// Se o state é uma URL completa, extrai o pathname
		if (decodedState.startsWith("http")) {
			const url = new URL(decodedState);
			return url.pathname + url.search;
		}
		
		// Se o state é um path relativo válido
		if (decodedState.startsWith("/")) {
			return decodedState;
		}
		
		return null;
	} catch (error) {
		console.warn("[auth/callback] Erro ao extrair redirect do state:", error);
		return null;
	}
}

function handleBackendError(response: IBackendTokenResponse): NextResponse {
	console.error("Erro na API do backend:", {
		status: response.status,
		response: response.errorText,
	});

	return createErrorResponse("backend_error", "Erro na comunicação com o servidor de autenticação", 500);
}
